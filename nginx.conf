
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
  include       mime.types;
  default_type  application/octet-stream;
  sendfile        on;
  keepalive_timeout  65;
	client_max_body_size 200m;
    #gzip  on;
	underscores_in_headers on;

	server {
		listen 80;
		server_name localhost;
		# gzip config
		location / {
			# 用于配合 browserHistory 使用
			root   /usr/share/nginx/html;
			index  index.html index.htm;
			try_files $uri $uri/ /index.html;
		}

	   location ~* ^/(code|auth|admin|gen|job|tx|act|basic|synchro|mes|sunlikecode|barcode|hotel|srm) {
       proxy_pass http://online-gateway:9999;
       #proxy_set_header Host $http_host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

	}



}
