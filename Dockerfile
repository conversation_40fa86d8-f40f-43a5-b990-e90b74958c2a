FROM nginx
MAINTAINER  lilong<<EMAIL>> 
ENV TZ=Asia/Shanghai
RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
COPY t/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
EXPOSE 443


#推送镜像至远程仓库时登录此账号
#docker login --username=docker@1936873205840135 registry.cn-shanghai.aliyuncs.com
#密码
#Sunlike.31260870

#docker run -d  -p 8000:80 online-ui
#docker build -t registry.cn-shanghai.aliyuncs.com/linux_sunlike/online-ui .

#docker tag online-ui:latest  registry.cn-shanghai.aliyuncs.com/linux_sunlike/online-ui:latest
#docker push  registry.cn-shanghai.aliyuncs.com/linux_sunlike/online-ui:latest


## 删除停止容器
#docker ps -a | grep 'Exited' | awk '{print $1}' | xargs docker stop | xargs docker rm    
## 删除 none 容器
#docker images | grep none | awk '{print $3}' | xargs docker rmi


# 杀死所有正在运行的容器
# docker kill $(docker ps -a -q)


# 删除所有已经停止的容器
# docker rm $(docker ps -a -q)


# 删除所有未打 dangling 标签的镜像
# docker rmi $(docker images -q -f dangling=true)


# 删除所有镜像
# docker rmi $(docker images -q)

#  强制删除镜像名称中包含“doss-api”的镜像
# docker rmi --force $(docker images | grep online | awk '{print $3}')





# 　删除所有未使用数据

# docker system prune

# 　　只删除未使用的volumes

# docker volume prune




#curl -L https://github.com/docker/compose/releases/download/1.25.0-rc4/docker-compose-`uname -s`-`ur/local/bin/docker-compose
