<template>
  <div>
    <div
      v-if="flag"
      @click="onClick"
    >{{$t('prdiversion.batchSelecsup')}}
    </div>
    <a-row
      width="100%"
      v-else
    >
      <a-col :span="24">
        <a-input-search
          :id='id'
          :autoFocus="autoFocus"
          :defaultActiveFirstOption="defaultActiveFirstOption"
          :disabled="disabled"
          :readOnly="readOnly"
          :dropdownMatchSelectWidth="dropdownMatchSelectWidth"
          :maxTagCount="maxTagCount"
          :maxTagTextLength="maxTagTextLength"
          :type="type"
          allowClear
          :multiple="multiple"
         
          :showArrow="showArrow"
          :size="size"
          :placeholder="placeholder"
          optionFilterProp="children"
          :getPopupContainer="
            triggerNode => {
              return triggerNode.parentNode || document.body
            }
          "
          :filterOption="filterOption"
          @search="onClick"
          v-model="value"
        >
          <a-tooltip
            slot="suffix"
            title="清空"
            v-if="!disabled"
          >
            <a-icon
              v-if="allowClear"
              @click="clear"
              type="close-circle"
              style="color: rgba(0,0,0,.45)"
            />
          </a-tooltip>
          <a-button
            ref="btn"
            :disabled="disabled"
            slot="enterButton"
          >
            <a-icon type="database" />
          </a-button>
        </a-input-search>
      </a-col>
    </a-row>

    <!-- 添加弹出框 -->
    <select-modal
      ref="modal"
      @onOk="onOk"
      @touch="touch($event)"
      :equipmentstation="equipmentstation"
      :urls="urls"
      :tableForm="tableForm"
      :tableColumn="tableColumn"
      :multiple="multiple"
      v-on:child-say="listenToMyBoy"
      :selecttransmit="selecttransmit"
    />
  </div>
</template>
<script>
import selectModal from './selectModal'

export default {
  name: 'SelectList',
  components: {
    selectModal
  },
  data () {
    return {
      obj: {},//列表选择后的对象
      value: '',
      urls: '',
      param: Array,
      column: Array,
      tableForm: Object,
      flag: false,
      cusNoList: [],
    }
  },
  watch: {
    datatype: {
      handler (val) {
        this.$nextTick(() => {
          this.value = this.datatype
        })
      },
      // 监听到数据变化时立即调用
      immediate: true
    },
    url () {
      this.urls = this.url
    }
  },
  props: {
    id: {
      type: String,
      required: false
    },
    // eslint-disable-next-line vue/require-default-prop
    placeholder: String,
    url: {
      type: String,
      required: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    // eslint-disable-next-line vue/require-default-prop
    params: Array,
    // eslint-disable-next-line vue/require-default-prop
    datatype: {
      type: String
    },
    // eslint-disable-next-line vue/require-default-prop
    // value: String,
    allowClear: {
      type: Boolean,
      default: false
    },
    autoFocus: Boolean,
    defaultActiveFirstOption: Boolean,
    // eslint-disable-next-line vue/require-default-prop
    defaultValue: [String, Number, Array],
    readOnly: {
      type: Boolean,
      default: true
    },
    disabled: Boolean,
    dropdownMatchSelectWidth: Boolean,
    // eslint-disable-next-line vue/require-default-prop
    maxTagCount: Number,
    // eslint-disable-next-line vue/require-default-prop
    maxTagTextLength: Number,
    // eslint-disable-next-line vue/require-default-prop
    type: String,
    showArrow: Boolean,
    // eslint-disable-next-line vue/require-default-prop
    size: String,
    equipmentstation:String,
    // eslint-disable-next-line vue/require-default-prop
    form: Object,
    // eslint-disable-next-line vue/require-default-prop
    name: String,
    // eslint-disable-next-line vue/require-default-prop
    // eslint-disable-next-line vue/require-default-prop
    tableColumn: Array,
    selecttransmit: Array,
  },
  created () {
    this.depId = this.id
    this.urls = this.url
    this.column = this.tableColumn
    this.tableForm = this.form

    if (window.location.href.indexOf("prdiversion") > -1) {
      this.flag = true
    }
  },

  methods: {
    touch (itm) {
      console.log(itm,'ghhhhhhh')
      //  if (this.name == 'salNo') {
      //   this.value = itm.data.salNo + '-' + itm.data.name
      // } else
      if (this.name == 'dep') {
        if(itm.data.deptCode){
          this.value = itm.data.deptCode + '-' + itm.data.name
        }
        if(itm.data.depName){
          this.value = itm.data.dep + '-' + itm.data.depName
        }
      }else if (this.name == 'cusNo1') {
        if(itm.data.cusNo){
          this.value = itm.data.cusNo + '-' + itm.data.cusName
        }
      }else if (this.name == 'sortNo') {
        if(itm.data.sortNo){
          this.value = itm.data.sortNo + '-' + itm.data.name
        }
      }else if (this.name == 'sbNo') {
        
        if(itm.data.sbNo){
          this.value = itm.data.sbNo + '-' + itm.data.name
        }
      }else if (this.name == 'byxmNo') {
        if(itm.data.byxmNo){
          this.value = itm.data.byxmNo + '-' + itm.data.byxmNm
        }
      }else if (this.name == 'byffNo') {
        if(itm.data.byffNo){
          this.value = itm.data.byffNo + '-' + itm.data.byffNm
        }
      }else if (this.name == 'bybwNo') {
        if(itm.data.bybwNo){
          this.value = itm.data.bybwNo + '-' + itm.data.bybwNm
        }
      }else if (this.name == 'ygNotwo') {
        this.value = itm.data.ygNo + '-' + '-' + itm.data.ygName
      }
      else if (this.name == 'bydjNo') {
        if(itm.data.bydjNo){
          this.value = itm.data.bydjNo + '-' + itm.data.bydjNm
        }
      }else if (this.name == 'typeNo') {
        if(itm.data.typeNo){
          this.value = itm.data.typeNo + '-' + itm.data.name
        }
      }else if (this.name == 'upSalNo') {
        this.value = itm.data.salNo + '-' + itm.data.name
      } else if (this.name == 'stationNo') {
        this.value = itm.data.stationNo + '-' + itm.data.stationName
      } else if (this.name == 'sebNo') {
        this.value = itm.data.sebNo + '-' + itm.data.itmSb + '-' + itm.data.name
      }else if (this.name == 'ygNo') {
        this.value = itm.data.salNo + '-' + '-' + itm.data.salName
      }else if (this.name == 'salNo') {
        this.value = itm.data.salNo + '-' + '-' + itm.data.name
      }else if (this.name == 'dep1') {
        if(itm.data.deptCode){
          this.value = itm.data.deptCode + '-' + itm.data.name
        }else{
          return
        }
      }else if (this.name == 'dep2') {
        if(itm.data.deptCode){
          this.value = itm.data.deptCode + '-' + itm.data.name
        }else{
          return
        }
        
      }
      const obj = {
        name: this.name,
        id: itm.id,
        value: itm.name,
        data: itm.data,
      }
      this.obj = obj
      this.$emit('choosetype', { obj })
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    onClick () {
      // fm部门人员权限判断部门id是否存在，如果存在可进入人员选择
      if (this.id === '') {
        this.$notification['warn']({
          message: this.$t('public.message'),
          description: '请先选择车间代号'
        })
      } else if (this.id === undefined) {
        this.$refs.modal.create({ title: '' })
      } else {
        this.$refs.modal.create({ title: '' })
      }

    },
    // 清空输入框内容
    clear () {
      
      this.value = ''
      const obj = {
        name: this.name,
        id: this.id,
        value: '',
        data: null,
        clear:1
      }
      this.$emit('choosetype', { obj }, 'clear')
      // 情空缓存
      this.$store.commit("CLEAR_SBANNO", []);
    },
    onOk () {
      this.getSelect()
    },
    listenToMyBoy (val) {
      this.cusNoList = val
      this.$emit('child-say2', val);

    }
  }
}
</script>
<style lang="less">
.el-input-group__append {
  padding: 0px !important;
  border: 0px !important;
}
</style>
