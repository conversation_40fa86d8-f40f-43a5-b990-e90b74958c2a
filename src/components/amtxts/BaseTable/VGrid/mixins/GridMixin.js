import { GridService } from '../services/GridService'
import { mapState } from 'vuex'

export default {
  props: {
    detailConfig: {
      type: Object,
      default: () => ({
        routerName: null,
        idField: null
      })
    },
    sortable: {
      type: Boolean,
      default: true
    },
    defineCreate: {
      type: Function,
      default: null
    },
    defineRemove: {
      type: Function,
      default: null
    },
    defineQuery: {
      type: Function,
      default: null
    },
    defineEdit: {
      type: Function,
      default: null
    },
    MODULE: {
      type: String,
      default: null
    },
  },

  data() {
    return {
      gridService: null,
      gridOptions: {},
      columnContent: [],
      sortContent: "",
      currentView: null
    }
  },

  mounted() {
    // 初始化表格配置服务
    this.gridService = new GridService(this)
    this.initGridOptions()
  },
  
  beforeDestroy() {
    this.gridService = null
  },
  
  computed: {
    // 表格列配置
    computedTableColumn() {
      if (this.gridService) {
        return this.gridService.buildTableColumns()
      }
      return []
    },
    
    MODULEValue() {
      return this.MODULE ?? (this.FUNID.includes('.') ? this.FUNID.split('.')[0] : 'admin')
    },

    ...mapState({
      tabCloseConfirmTip: state => state.tab.tabCloseConfirmTip
    })
  },
  
  methods: {
    // 初始化表格配置
    initGridOptions() {
      if (this.gridService) {
        this.gridService.initGridOptions()
      }
    },

    // 更新表格列配置
    updateGridColumns() {
      if (this.gridService) {
        this.gridService.updateGridColumns()
      }
    },

    // 应用排序
    applySorting() {
      if (this.gridService) {
        this.gridService.applySorting()
      }
    }
  },
  
  watch: {
    // 监听表格列变化
    computedTableColumn: {
      immediate: true,
      handler(columns) {
        if (this.gridOptions) {
          this.gridOptions.columns = columns
        }
      }
    }
  }
} 