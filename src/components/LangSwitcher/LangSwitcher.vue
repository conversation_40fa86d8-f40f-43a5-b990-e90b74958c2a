<template>
  <el-dropdown @command="handleCommand" class="header-wrapper" >
    <span class="el-dropdown-link">
      <el-row>
        <el-button type="primary" v-model="local.languageName" class="language-input" ><span>{{ local.languageName }}</span></el-button>
      </el-row>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :command="item" v-for="item in languages" :key="item.key">{{ item.name }}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>

</template>

<script>
import Vue from 'vue'
import Languages from './Languages.js'
import i18n from '@/locales'
import { DEFAULT_LANGUAGE, DEFAULT_LANGUAGE_NAME } from '@/store/mutation-types'
import { mixin as langMixin } from '@/store/i18n-mixin'
export default {
  name: 'LangSwitcher',
  mixins: [langMixin],
  data () {
    return {
      local: {
        language: '',
        languageName: ''
      },
      languages: [

      ]
    }
  },

  created () {
    this.languages = Languages.languages

    if (Vue.ls.get(DEFAULT_LANGUAGE) == null) {
      this.languages.map(v => {
        if (navigator.language === v.key) this.local.languageName = v.name
      })
      this.local.language = navigator.language
    } else {
      this.local.language = Vue.ls.get(DEFAULT_LANGUAGE)
      this.local.languageName = Vue.ls.get(DEFAULT_LANGUAGE_NAME)
    }
    this.setLang(this.local.language)
    this.locale = i18n.getLocaleMessage(this.local.language).antLocale
  },
  methods: {
    handleCommand (command) {
      this.local.languageName = command.name
      this.local.language = command.key
      Vue.ls.set(DEFAULT_LANGUAGE, this.local.language)
      Vue.ls.set(DEFAULT_LANGUAGE_NAME, this.local.languageName)
      this.locale = i18n.getLocaleMessage(command.key).antLocale
      this.setLang(command.key)
    }
  }
}
</script>
<style lang="less" scoped>
/* 自定义样式 */
.language-input{
  width: 106px; /* 调整输入框长度 */
  border-radius: 2px;
  height: 32px;
  background-color: white;
  display: flex;
  align-items: center;
  border: none;
  justify-content: center;
}
.language-input span{
  font-family: PingFangSC, PingFang SC;
  letter-spacing: 2px; /* 设置字间距 */
  font-weight: 540;
  font-size: 14px;
  color: #000000;
  padding-right: 5px;
  line-height: 25px;
  text-align: center;
  font-style: normal;
}
.language-input::after {
  content: '\25BC'; /* Unicode for down arrow */
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.5;
  pointer-events: none; /* 禁止点击事件 */
  font-size: 16px; /* 根据需要调整大小 */
  color: #000000; /* 箭头颜色 */
}
</style>