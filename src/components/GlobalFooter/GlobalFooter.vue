<template>
  <div
    class="footer"
    v-if="isShowFooter"
  >
    <div class="copyright">
      Copyright
      <a-icon type="copyright" /> 2020 <span></span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GlobalFooter',
  data () {
    return {
      isShowFooter: false
    }
  },
  computed: {
    // isShowFooter() {
    //   return (this.$route.path !== '/srm/invoice') || (this.$route.path !== '/srm/workers')
    // }
  },
  watch: {
    // $route (to, from) {
    //   if (to.path == '/srm/invoice' || to.path == '/srm/workers') {
    //     this.isShowFooter = false
    //   } else {
    //     this.isShowFooter = true
    //   }
    // }
  }
}
</script>

<style lang="less" scoped>
.footer {
  padding: 0 16px;
  margin: 48px 0 24px;
  text-align: center;

  .links {
    margin-bottom: 8px;

    a {
      color: rgba(0, 0, 0, 0.45);

      &:hover {
        color: rgba(0, 0, 0, 0.65);
      }

      &:not(:last-child) {
        margin-right: 40px;
      }
    }
  }
  .copyright {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }
}
</style>
