<template>
  <!-- @cancel="handleCancel" modal 关闭提示 -->
  <a-modal
    :title="title"
    destroyOnClose
    width="40%"
    :visible.sync="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
  >
    <!-- <a-form layout="inline" :form="form" >
      <a-form-item
        v-for="(itm, index) in list"
        :key="index">
        <a-input
          v-if="!itm.hidden"
          v-decorator="[
            `${itm.value}`
          ]"
          :placeholder="`${itm.placeholder}`"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="getList">{{ $t('public.query') }}</a-button>
        <a-button style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</a-button>
      </a-form-item>
    </a-form> -->

    <vxe-grid
      border
      resizable
      size="small"
      show-overflow
      max-height="450px"
      highlight-hover-row
      highlight-current-row
      ref="xTable"
      :form-config="tableForm"
      :radio-config="{labelField: '', trigger: 'row'}"
      @cell-click="cellClickEvent"
      :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
      :checkbox-config="{trigger: 'row', range: true}"
      :pager-config="tablePage"
      :columns="tableColumn"
      :data="tableData"
      @page-change="handlePageChange"
      @form-submit="search"
    ></vxe-grid>
    <template slot="footer">
      <a-button
        key="ok"
        @click="save"
      >{{ $t('public.sure') }}</a-button>
      <a-button
        key="cancel"
        @click="handleCancel"
      >{{ $t('public.cancel') }}</a-button>
    </template>
  </a-modal>
</template>

<script>
import axios from '@/router/axios'
import { fetchBilNo } from '@/api/srm/invoice'
import { inquirysave } from '@/api/srm/prdiversion'
export default {
  name: 'SelectModal',
  props: {
    urls: {
      type: String,
      required: true
    },
    multiple: {
      type: Boolean,
      required: true
    },
    // eslint-disable-next-line vue/require-default-prop
    tableColumn: Array,
    // eslint-disable-next-line vue/require-default-prop
    tableForm: Object,
    selecttransmit: Array,
  },
  data () {
    return {
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'Total'],
        perfect: true
      },
      id: '',
      row: {},
      confirmLoading: false,
      field: '',
      cellValue: '',
      title: '',
      visible: false,
      tableData: [],
      mfXjFbSlVoList: [],
      datarecord: []
    }
  },

  created () {
  },
  methods: {
    search () {
      this.tablePage.currentPage = 1
      this.getList(this.tablePage)
    },
    getList (tablePage) {
      axios({
        url: this.urls,
        method: 'get',
        params: Object.assign({
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
        }, this.tableForm.data)
      }).then(res => {
        if (res.data) {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
        }
      })
    },
    // 分页
    handlePageChange ({
      currentPage,
      pageSize
    }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList(this.tablePage)
    },
    cellClickEvent ({
      row
    }) {
      this.row = row
    },
    create (model) {
      this.title = model.title
      this.visible = true
      this.getList()
    },
    // 添加确认

    save () {
      // 多选状态
      if (this.multiple || window.location.href.indexOf("prdiversion") > -1) {
        const selectRecords = this.$refs.xTable.getCheckboxRecords()
        // 询价发布供应商
        if (window.location.href.indexOf("prdiversion") > -1) {
          this.$emit('child-say', selectRecords);
          // this.datarecord = JSON.parse(localStorage.getItem("keyrecords"))
        }
        // 以上询价发布供应商
        const name = []
        const id = []
        selectRecords.forEach(i => {
          name.push(i.name || i.roleName)
          id.push(i.id)
        })
        this.$emit('touch', {
          name: name.join(','),
          id: id,
          data: selectRecords
        })
        if (window.location.href.indexOf("prdiversion") > -1) {
          this.$message.success('操作成功！')
        } else {
          this.visible = false
        }
      } else {
        // 单选状态
        // 根据不同数据传递来的代号查询id
        // 采购对账开票接口
        // if (window.location.href.indexOf("invoice") > -1 || window.location.href.indexOf("workers") > -1) {
        // fetchBilNo({ cusNo: this.row.cusNo })
        //   .then((res) => {
        //     this.$emit('accbill', res.data)
        //   })
        //   .catch((err) => {
        //     this.loading = false
        //     this.requestFailed(err)
        //   })
        // }
        //采购对账接口
        this.$emit('touch', {
          name: this.row.name || this.row.recName || this.row.zcName,
          id: this.row.id,
          data: this.row
        })
        this.visible = false
        this.id = ''
        this.tableForm.data[Object.keys(this.tableForm.data)[0]] = ''
        this.tableForm.data[Object.keys(this.tableForm.data)[1]] = ''

      }
    },
    handleCancel () {
      this.tableForm.data[Object.keys(this.tableForm.data)[0]] = ''
      this.tableForm.data[Object.keys(this.tableForm.data)[1]] = ''
      this.tablePage.currentPage = 1
      this.tablePage.pageSize = 10
      this.visible = false
    }

  }
}
</script>
