<template>
	<el-dialog title="选择" :visible.sync="visible" :before-close="handleCancel" width="40%" destroy-on-close
		append-to-body class="JustMake-dialog">
		<vxe-grid border resizable size="small" show-overflow max-height="450px" highlight-hover-row
			highlight-current-row ref="xTable" :form-config="tableForm"
			:radio-config="{ labelField: '', trigger: 'row', highlight:true }"
			:seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
			:checkbox-config="{ trigger: 'row', range: true }" :pager-config="tablePage" :columns="tableColumn"
			:data="tableData" @page-change="handlePageChange" @form-submit="search" @cell-dblclick="save"></vxe-grid>

		<span slot="footer" class="dialog-footer">
			<el-button @click="handleCancel">{{ $t('public.cancel') }}</el-button>
			<el-button type="primary" @click="save">{{ $t('public.sure') }}</el-button>
		</span>
	</el-dialog>
</template>

<script>
	import axios from '@/router/axios'
	export default {
    name: 'SelectModal',
    props: {
      urls: {
        type: String,
        required: true
      },
      multiple: {
        type: Boolean,
        required: true
      },
      method: {
        type: String,
        default: 'GET',
      },
      tableColumn: Array,
      tableForm: Object,
    },
    data() {
      return {
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          total: 0,
          pageSizes: [10, 20, 50, 100, 200, 500],
          layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'Total'],
          perfect: true
        },
        id: '',
        row: {},
        confirmLoading: false,
        field: '',
        cellValue: '',
        title: '',
        visible: false,
        tableData: [],
        mfXjFbSlVoList: [],
        datarecord: []
      }
    },
    methods: {
      search() {
        this.tablePage.currentPage = 1
        this.getList()
      },
      // 公共的请求方法
      fetchData(url, params = {}) {
        const linkStr = url.includes('?') ? '&' : '?';
        const _url = url + linkStr + 'size=' + this.tablePage.pageSize + '&current=' + this.tablePage.currentPage;

        return axios({
          url: _url,
          method: this.method,
          [this.method.toLowerCase() === 'get' ? 'params' : 'data']: params,
        });
      },

      // 获取列表数据
      getList() {
        try{
          this.fetchData(this.urls, this.tableForm.data)
            .then(res => {
              if (res.data) {
                this.tableData = res.data.records || res.data;
                this.tablePage.total = res.data.total || res.data.length;
                this.tablePage.currentPage = res.data.current || 1;
              }
            })
        }catch (err){
          this.requestFailed(err)
        }

      },
      // 快速查询
      quickSearch(searchValue) {
        if(!searchValue) return
        this.fetchData(this.urls, {[Object.keys(this.tableForm.data)[0]]: searchValue}) // 快速查询只传递第一个字段的数据
          .then(res => {
            const result = res.data.records || res.data;
            this.triggerSave({name: result[0].name, id: result[0].id, data: result[0]});
          })
      },
      // 分页
      handlePageChange({currentPage,pageSize}) {
        this.tablePage.currentPage = currentPage
        this.tablePage.pageSize = pageSize
        this.getList()
      },
      create(param) {
        this.visible = true
        this.getList()
      },
      save() {
        const selectRecords = this.multiple ? this.$refs.xTable.getCheckboxRecords() : [this.$refs.xTable.getRadioRecord()];

        if (this.multiple) { // 多选
          if (selectRecords.length === 0) return;
          const name = selectRecords.map(i => i.name || i.roleName);
          const ids = selectRecords.map(i => i.id);
          this.triggerSave({ name: name.join(','), ids, data: selectRecords });
        } else { // 单选
          if (!selectRecords[0]) return;
          this.triggerSave({name: selectRecords[0].name, id: selectRecords[0].id, data: selectRecords[0]});
          // 清空 tableForm 的数据
          const keys = Object.keys(this.tableForm.data);
          this.tableForm.data[keys[0]] = '';
          this.tableForm.data[keys[1]] = '';
        }

        this.visible = false;
      },
      triggerSave({ name, id, data }) {
        this.$emit('touch', { name, id, data });
      },
      handleCancel() {
        if (this.tableForm && this.tableForm.data) {
          const keys = Object.keys(this.tableForm.data);
          if (keys.length > 0) {
            this.tableForm.data[keys[0]] = '';
          }
          if (keys.length > 1) {
            this.tableForm.data[keys[1]] = '';
          }
        }
        this.tablePage.currentPage = 1;
        this.tablePage.pageSize = 10;
        this.visible = false;
      }
    }
  }
</script>

<style lang="less" scoped>
	::v-deep .el-dialog {
		.el-dialog__body {
			padding: 16px 16px 0px 16px;

		}

		.vxe-button.type--button.theme--primary:not(.is--disabled){
			background-color: var(--el-color-primary);
			border-color: var(--el-color-primary);
		}

		.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active {
			color: #fff;
			background-color: var(--el-color-primary);
		}
    .vxe-body--row.row--radio{
      background-color: var(--el-color-primary-light-8);
    }
	}
</style>