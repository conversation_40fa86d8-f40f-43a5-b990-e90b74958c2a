<template>
	<div class='vxe-table--ignore-clear'>
      <el-input ref="input"  :autofocus="autoFocus" :readonly="readOnly" :clearable="!readOnly" :disabled="disabled" :type="type"
			:placeholder="placeholder" v-model="VmodelValue" suffix-icon="el-icon-search" @clear="clear">
		</el-input>
		<!-- 添加弹出框 -->
		<select-dialog class='vxe-table--ignore-clear' ref="modal" @onOk="onOk" @touch="touch($event)" :method='method' :urls="url" :tableForm="form"
			:tableColumn="tableColumn" :multiple="multiple"/>
	</div>
</template>
<script>
import SelectDialog from './selectDialog'
export default {
	name: 'SelectList',
	components: {
		SelectDialog
	},
  computed:{
    VmodelValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
	props: {
    data:Object,
		value: {
			type: [String, Number, Boolean, Array, Object],
		},
		placeholder: String,
		url: {
			type: String,
			required: true
		},
		multiple: {
			type: Boolean,
			default: false
		},
    method:{
      type: String,
      default: 'GET',
    },
		autoFocus: Boolean,
		readOnly: {
			type: Boolean,
			default: false
		},
		disabled: Boolean,
		type: String,
		form: Object,
		name: String,
		tableColumn: Array,
	},
	mounted() {
		this.$nextTick(() => {
			const suffix = this.$refs.input.$el.querySelector('.el-icon-search');
			if (suffix) {
				suffix.style.cursor = 'pointer';
				suffix.addEventListener('click', this.onClick);
			}
		});
    this.$refs.input.$el.onkeydown = async (e) => {
      if (e.keyCode === 13) { // 监听回车事件
        await this.$refs.modal.quickSearch(this.VmodelValue);

      }
    };
  },
	beforeDestroy() {
		const suffix = this.$refs.input.$el.querySelector('.el-icon-search');
		if (suffix) {
			suffix.removeEventListener('click', this.onClick);
		}
	},
	methods: {
    // 选择事件
		touch(itm) {
      this.$emit('input', itm?.name);
			this.$emit('selectListEvent', { code: 'choose', obj: itm, field: this.data.field})
		},
		onClick() {
			this.$refs.modal.create(this.VmodelValue)
		},
		// 清空输入框内容
		clear() {
      this.$emit('input', ''); // 置null会导致后端更新忽略该字段
			this.$emit('selectListEvent', { code: 'clear', obj: null, field: this.data.field })
			// 情空缓存
			this.$store.commit("CLEAR_SBANNO", []);
		},
		onOk() {
			this.getSelect()
		},
	},
}
</script>
<style lang="less" scoped>
::v-deep .el-input-group__append {
	padding: 0px !important;
	border: 0px !important;
}
</style>
