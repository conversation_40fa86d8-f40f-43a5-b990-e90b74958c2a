<template></template>

<script>
import axios from 'axios'
// import XLSX from 'xlsx'
import moment from 'moment'
export default {
  data() {
    return {
      listAll: [],
      bjStatus: null,
    }
  },
  methods: {
    create(obj) {
      const hide = this.$message.loading('导出中..', 0)
      let params = {
        current: 1,
        size: -1,
        ...obj.queryParam
      }
      axios({
        method: 'get',
        url: obj.url,
        params: params
      })
        .then((res) => {
          setTimeout(hide, 10)
          this.$message.success('导出成功')
          this.listAll = res.data.records
          const filterVal = [...obj.eng]
          const list = this.listAll
          const data = this.formatJson(filterVal, list)
          const colWidth = data.map((row) =>
            row.map((val) => {
              if (val == null) {
                return { wch: 10 }
              } else if (val.toString().charCodeAt(0) > 255) {
                return { wch: val.toString().length + 5 }
              } else {
                return { wch: val.toString().length + 5 }
              }
            })
          )
          let result = colWidth[0]
          for (let i = 1; i < colWidth.length; i++) {
            for (let j = 0; j < colWidth[i].length; j++) {
              if (result[j]['wch'] < colWidth[i][j]['wch']) {
                result[j]['wch'] = colWidth[i][j]['wch']
              }
            }
          }
          var aoa = [
            ['开始时间:', null, moment(obj.startDd).format('YYYY-MM-DD')],
            ['结束时间:', null, moment(obj.endDd).format('YYYY-MM-DD')],
            ['申请单号:', null, obj.queryParam.sqNo],
            ['文件编号:', null, obj.queryParam.fileNo],
            [],
            [...obj.zh],
          ]
          var c = aoa.concat(data)
          var sheet = XLSX.utils.aoa_to_sheet(c)
          sheet['!merges'] = [
            // 设置A1-C1的单元格合并
            { s: { r: 0, c: 0 }, e: { r: 0, c: 1 } },
            { s: { r: 0, c: 1 }, e: { r: 0, c: 1 } },
            { s: { r: 0, c: 1 }, e: { r: 0, c: 1 } },
            { s: { r: 0, c: 1 }, e: { r: 0, c: 1 } },
          ]
          sheet['!cols'] = result
          let blob = this.sheet2blob(sheet)
          this.openDownloadDialog(blob, obj.name + '.xlsx')
        })
        .catch((err) => {
          setTimeout(hide, 10)
          this.$message.error('导出失败')
        })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]))
    },
    sheet2blob(sheet, sheetName) {
      sheetName = sheetName || 'sheet1'
      var workbook = {
        SheetNames: [sheetName],
        Sheets: {},
      }
      workbook.Sheets[sheetName] = sheet
      // 生成excel的配置项
      var wopts = {
        bookType: 'xlsx', // 要生成的文件类型
        bookSST: false, // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
        type: 'binary',
      }
      var wbout = XLSX.write(workbook, wopts)
      var blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' })
      // 字符串转ArrayBuffer
      function s2ab(s) {
        var buf = new ArrayBuffer(s.length)
        var view = new Uint8Array(buf)
        for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
        return buf
      }
      return blob
    },
    openDownloadDialog(url, saveName) {
      if (typeof url == 'object' && url instanceof Blob) {
        url = URL.createObjectURL(url) // 创建blob地址
      }
      var aLink = document.createElement('a')
      aLink.href = url
      aLink.download = saveName || '' // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
      var event
      if (window.MouseEvent) event = new MouseEvent('click')
      else {
        event = document.createEvent('MouseEvents')
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
      }
      aLink.dispatchEvent(event)
    },
  },
}
</script>

<style>
</style>