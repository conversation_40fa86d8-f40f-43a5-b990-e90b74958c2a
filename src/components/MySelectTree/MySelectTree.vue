<template>
  <div>
    <a-tree-select
      :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
      :treeData="gData"
      @loadData="onLoadData"
      allowClear
    >
    </a-tree-select>
  </div>
</template>
<script>
import axios from '@/router/axios'
export default {
  name: 'MySelectTree',
  data () {
    return {
      gData: []
    }
  },
  props: {
    url: {
      type: String,
      required: true
    },
    params: {
      type: String,
      required: true
    }
  },
  created () { this.getTree() },
  methods: {
    onLoadData (treeNode) {
      return new Promise(resolve => {
        var map = {}
        if (treeNode.dataRef !== undefined) {
          map[this.params] = treeNode.dataRef.id
        }
        axios({
          url: this.url,
          method: 'get',
          params: map
        }).then(res => {
          if (res.data.length === 0) {
            resolve()
          } else {
            if (treeNode.dataRef !== undefined) {
              treeNode.dataRef.children = res.data
            }
            resolve()
          }
        })
      })
    },
    getTree () {
      this.gData = []
      axios({
        url: this.url,
        method: 'get'
      }).then(res => {
        this.gData = res.data
      })
    },

    select (selectedKeys, e) {
      this.$emit('select', e.node)
    },
    onExpand (expandedKeys, expand) {
      // if (expand.expanded) {
      //   expand.node.dataRef.children = []
      //   this.onLoadData(expand.node)
      // }
    },
    onrightClick (e) {
      // this.showMenu = true
    }
  }
}
</script>
