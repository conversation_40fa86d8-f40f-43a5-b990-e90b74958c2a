<template>
  <div>
    <el-dialog :title="$t('arc.componentsPrintDialog.index.title')" style='padding: 0px;' width='800px'  :before-close='cancelEidt' :visible.sync='dialogVisible'
      :destroy-on-close='true' :close-on-click-modal='false' v-if='dialogVisible'>
      <div style="margin-top: 15px;">
        <el-form ref='templateFrom' label-position="left" label-width="auto" size="mini" class="template_from">
          <el-row :gutter="10">
            <el-col :span="20" :xs="24">
              <el-form-item :label="$t('arc.componentsPrintDialog.index.printTemplate')" label-width="90px">
                <el-input v-model="code" size="small" placeholder="">
                  <template #suffix>
                    <i class="el-icon-search" @click="handleIconClick"></i>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="2" :xs="24">
              <el-button type="primary" size="small" @click="handleEdit" plain>{{ $t('public.edit') }}</el-button>
            </el-col>
            <el-col :span="2" :xs="24">
              <el-button type="primary" size="small" @click="handleCrate" plain>{{ $t('public.add') }}</el-button>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="22" :xs="24">
              <el-form-item v-if="selectFiled.type==='date'" :label='selectFiled.label' label-width="90px">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  size="small"
                  type="daterange"
                  align="right"
                  v-model="selectFiled.name"
                  unlink-panels range-separator="-"
                  :start-placeholder="$t('arc.componentsPrintDialog.index.startPlaceholder')"
                  :end-placeholder="$t('arc.componentsPrintDialog.index.结束日期')"
                  :picker-options="pickerOptions"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item v-else-if="selectFiled.type==='String'" :label='selectFiled.label' label-width="90px">
                <el-input v-model="selectFiled.name" size="small" placeholder="" > </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="2" :xs="24">
              <el-button type="primary" size="small"  @click="handleSelect">{{ $t('public.query') }}</el-button>
            </el-col>
          </el-row>
        </el-form>
        <div>
          <vxe-grid ref="gridRef" border stripe resizable show-overflow show-header-overflow height="400px"
                    :loading="loading"
                    :columns="columns"
                    :data="tableData"
                    :pager-config="pagerConfig"
                    @page-change="handlePageChange"
                    @checkbox-change="handleCheckboxChange"
                    @checkbox-all="handleCheckboxAll" >
          </vxe-grid>
        </div>
      </div>
<!--            <div slot="footer" style="margin: 10px">-->
<!--              <el-button @click="dialogVisible = false" size="small" style="margin-bottom: 10px;">关闭(ESC)</el-button>-->
<!--              <el-button @click="previewEvent" size="small" style="margin-bottom: 10px;">预览(F6)</el-button>-->
<!--              <el-button @click="loadViewer" type="primary" size="small" style="margin-bottom: 10px;">打印(F7)</el-button>-->
<!--            </div>-->

      <div slot="footer" class="dialog-footer">
        <el-checkbox lass="edit_print" v-model="merge" >{{ $t('arc.componentsPrintDialog.index.mergePrinting') }}</el-checkbox>
        <div class="footer-buttons">
          <el-button @click="dialogVisible = false" size="small">{{ $t('arc.componentsPrintDialog.index.closeESC') }}</el-button>
          <el-button @click="previewEvent" size="small">{{ $t('arc.componentsPrintDialog.index.preview') }}</el-button>
          <el-button @click="loadViewer" type="primary" size="small">{{ $t('arc.componentsPrintDialog.index.print') }}</el-button>
        </div>
      </div>
      <el-dialog height="400px" width="800px" :title="$t('arc.overprint.printTemplate.select')" :visible.sync='selectVisible' :close-on-click-modal='false'
        :destroy-on-close='true' append-to-body>
        <div class="sync-dialog__div">
          <PrintTemplate @selectId="handleSelectClick" :menuId="String(this.menuId)"></PrintTemplate>
        </div>
      </el-dialog>
    </el-dialog>
    <el-dialog top="6vh" :title="$t('arc.componentsPrintDialog.index.dialogTitlePrintPreview')" :visible.sync='visible' :close-on-click-modal='false' :destroy-on-close='true'
      width='80%'>
      <div class="input-container">
        <label for="document-code">{{columns[0] && columns[0].label + ':' }}</label>
        <el-select v-model="djno" :placeholder="$t('arc.componentsPrintDialog.index.select')" id="document-code" class="document-input" size="small"
                   @change="upload"
                   style="border-color: rgb(178, 178, 178);">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
<!--        <el-input v-model="djno" id="document-code" class="document-input" size="small"-->
<!--          style="border-color: rgb(178, 178, 178);"></el-input>-->
      </div>
      <ArViewer ref='arViewer' :report-id='reportId' :is-full-screen='fullScreen' />
      <div slot="footer" class="dialog-footer">
        <el-button @click="printEdit" size="small" class="edit_print">{{ $t('arc.componentsPrintDialog.index.templateEdit') }}</el-button>
        <div class="footer-buttons">
          <el-button @click="visible = false" size="small">{{ $t('arc.componentsPrintDialog.index.close') }}</el-button>
          <el-button type="primary" @click="exPrint" size="small">{{ $t('arc.componentsPrintDialog.index.title') }}</el-button>
        </div>
      </div>
    </el-dialog>
    <CreateTemplate ref="createTemplateRef"></CreateTemplate>
  </div>
</template>

<script>
import { getPrintInfo,selectPrintDataSources } from '@/api/interfaceList'
import ArViewer from '@/components/printDialog/ArViewer'
import PrintTemplate from './selectPrintTemplate'
import { serialize } from '@/utils/util'
import Vue from 'vue';
import CreateTemplate from '@/views/custommana/overprint/createTemplate.vue'
import { getMneuByPath } from '@/api/admin/menu'
import { processPrintRequests,sleep } from '@/utils/arPrintAgent'
export default {
  components: {
    ArViewer, PrintTemplate, CreateTemplate
  },
  data() {
    return {
      batchPrinting:false,//批量打印
      merge:false,//合并打印
      fullScreen: false,
      reportId: '',
      visible: false,
      selectVisible: false,
      menuId: '',
      menuName: '',
      code: '',
      djno: '',
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      printTemplate: {},
      border: true,
      dialogVisible: false,
      tableData: [],
      selectFiled: {},
      columns:[],
      multipleSelection:[], // 用于存储选中的行数据
      options:[],
      disPlayFieldIdName:'', //表格的查询Id名字
      pagerConfig: {
        layouts: ['Total', 'Sizes', 'PrevPage', 'JumpNumber', 'NextPage', 'FullJump'],
        total: 0,
        currentPage: 1,
        pageSize: 100,
        pageSizes: [50, 100, 200, 500,1000,1500] // 每页显示条数的下拉框选项
      },
      loading: false,
      // 开启多选功能
      checkboxConfig: {
        checkField: 'checked', // 可选，指定用于存储选中状态的字段
        highlight: true // 可选，选中行时是否高亮显示
      },
    }
  },
  mounted() {
    // 添加键盘事件监听器
    window.addEventListener('keydown', this.handleKeydown);
  },
  // beforeDestroy() {
  //   // 移除键盘事件监听器
  //   window.removeEventListener('keydown', this.handleKeydown);
  // },
  methods: {
    handleKeydown(event) {
      if (event.key === 'F6') {
        this.previewEvent()
      } else if (event.key === 'F7') {
        this.loadViewer()
      }
    },
    //打印弹窗打印套版输入框按钮
    handleIconClick() {
      this.selectVisible = true
    },
    //选择弹窗选择按钮
    handleSelectClick(prams) {
      this.selectVisible = false
      this.printTemplate = prams
      Vue.set(this,'printTemplate', prams)
      this.code = prams.code
    },
    //打印弹窗关闭回调
    cancelEidt() {
      this.dialogVisible = false
    },
    //父窗口调用方法
    create: function(printData) {
      let _that = this
      this.dialogVisible = true
      //根据路由查找菜单ID
      getMneuByPath({ path: printData.path }).then(res => {
        _that.menuId = res.data.menuId
        _that.menuName = res.data.name
        //根据菜单ID查询打印查询框信息
        getPrintInfo(this.menuId).then(res => {
          const selectField = res.data.selectFiled
          if (selectField.type === 'date') {
            const  today  = new Date()
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            const day = String(today.getDate()).padStart(2, '0');
            selectField.name = [`${year}-${month}-${day}`,`${year}-${month}-${day}`];
          } else if (selectField.type === 'String') {
            selectField.name = '';
          }
          Vue.set(_that, 'selectFiled', selectField)
          let dpfss = []
          dpfss.push({
            type: 'checkbox',
            width: 50,
            align: 'center'
          })
          dpfss.push({
            type: 'seq',
            title: '序号',
            width: 80,
            align: 'center'
          })
          for (let i = 0; i < res.data.disPlayField.length; i++) {
            let disPlayFields  = res.data.disPlayField[i]
            let disPlayField = {
              title: disPlayFields.label,
              field: disPlayFields.name
            }
            dpfss.push(disPlayField)
          }
          _that.$nextTick(() => {
            this.$refs.gridRef
            Vue.set(_that, 'columns', dpfss)
            Vue.set(_that, 'disPlayFieldIdName', res.data.disPlayField[0].name)
          })
        })
      }).catch(err => {
        this.requestFailed(err)
      })
    },
    //打印弹窗表格多选事件
    handleSelectionChange(val){
      if (val.length > 1)
        this.batchPrinting = true
      else
        this.batchPrinting = false
      this.multipleSelection = val
    },
    //打印弹窗编辑按钮
    handleEdit() {
      if (this.code != null && this.code.length > 0) {
        this.dialogVisible = false
        const username = this.$store.state.user.info.username
        //路由参数放缓存
        localStorage.setItem(username + "_bill_type", this.printTemplate.bill_type)
        localStorage.setItem(username + "_template_id", this.printTemplate.id)
        localStorage.setItem(username + "_refresh_print", true)//刷新页面标识
        this.$router.push({
          name: 'arcmodel',
          params: { id: this.printTemplate.id, name: this.printTemplate.name, reportId: this.printTemplate.id, bill_type: this.printTemplate.bill_type,subType:this.printTemplate.type },
          replace: true
        })
      }
    },
    //打印弹窗新增按钮
    handleCrate() {
      this.dialogVisible = false
      let entity = {}
      entity.is_default = false
      entity.language_code = 'zh-hans'
      entity.bill_type = this.menuId
      entity.bill_type_name = this.menuName
      this.$refs.createTemplateRef.setCreateVisible(true, entity)
    },
    //打印弹窗查询按钮
    handleSelect() {
      let params;
      if (this.selectFiled.type == 'date'){
        if (Array.isArray(this.selectFiled.name)) {
          params = this.selectFiled.name[0] + "`" + this.selectFiled.name[1]
        }else {
          return;
        }
      }else {
        params =this.selectFiled.name
      }
      selectPrintDataSources({params:params,bill_type: this.menuId,current: this.pagerConfig.currentPage,size: this.pagerConfig.pageSize}).then(res =>{
        this.$nextTick(() => {
          this.tableData = res.data.records || res.data
          this.pagerConfig.total = res.data.total
          this.pagerConfig.currentPage = res.data.current
          // 强制更新vxe-grid组件
          this.$refs.gridRef.refresh()
        })
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      let params;
      this.pagerConfig.pageSize = pageSize
      if (this.selectFiled.type == 'date'){
        if (Array.isArray(this.selectFiled.name)) {
          params = this.selectFiled.name[0] + "`" + this.selectFiled.name[1]
        }else {
          return;
        }
      }else {
        params =this.selectFiled.name
      }
      selectPrintDataSources({params:params,bill_type: this.menuId,current: currentPage,size: pageSize}).then(res =>{
        this.tableData = res.data.records || res.data
        this.pagerConfig.total = res.data.total
        this.pagerConfig.currentPage = res.data.current
        // 强制更新vxe-grid组件
        this.$refs.gridRef.refresh()
      })
    },
    //打印弹窗表格多选事件
    handleCheckboxChange(data, row){
      if (data.records.length > 1)
        this.batchPrinting = true
      else
        this.batchPrinting = false
    },
    handleCheckboxAll(data) {
      if (data.records.length > 1)
        this.batchPrinting = true
      else
        this.batchPrinting = false
    },
    getCheckboxRecords() {
      return this.$refs.gridRef.getCheckboxRecords()
    },
    //打印弹窗预览按钮
    async previewEvent() {
      let _that = this
      if (this.code != null && this.code.length > 0) {
        if (this.getCheckboxRecords().length === 0) return this.$message.error(this.$t('arc.index.errorMsgSingleItem'))
        this.options = []
        if (!this.merge){
          this.getCheckboxRecords().forEach((i, index) => {
            let option = {
              value: i[this.disPlayFieldIdName],
              label: i[this.disPlayFieldIdName]
            }
            if (index === 0){
              Vue.set(this,'djno',option.value);
            }
            this.options.push(option)
          })
        }else {
          let ids = '';
          this.getCheckboxRecords().forEach((i, index) => {
            ids += i[_that.disPlayFieldIdName]+","
          })
          ids = ids.substring(0,ids.length-1)
          this.options = [{
            values: ids,
            label:ids
          }]
          Vue.set(this,'djno',ids);
        }
        this.visible = true
        await this.$nextTick(); // 等待 DOM 更新
        this.preview() //当前this会丢失问题，所以换个方法
      }else {
        this.$message.error(this.$t('arc.index.errorMsgSelectTemplate'))
      }
    },
    preview() {
      let params = 'timeKey=1&bill_type=' + this.printTemplate.bill_type + '&condition=id:' + this.djno
      const URI = encodeURIComponent(params)
      this.reportId = this.printTemplate.id + '|' + URI
      const arViewer = this.$refs.arViewer;
      if (arViewer && typeof arViewer.openReportEvent === 'function') {
        arViewer.openReportEvent(this.reportId);
      } else {
        console.error('openReportEvent is not a function or arViewer is undefined.');
      }
    },
    //打印弹窗打印按钮
    loadViewer() {
      if (this.code != null && this.code.length > 0) {
        if (this.getCheckboxRecords().length === 0) return this.$message.error(this.$t('arc.index.errorMsgSingleItem'))
        if (!this.merge) { //不合并默认打印第一条
          this.getCheckboxRecords().forEach((i, index) => {
            if (index === 0){
              Vue.set(this,'djno',i[this.disPlayFieldIdName]);
            }
          })
        }else {
          let ids = '';
          this.getCheckboxRecords().forEach((i, index) => {
            ids += i[this.disPlayFieldIdName]+","
          })
          ids = ids.substring(0,ids.length-1)
          Vue.set(this,'djno',ids);
        }
        //判断是否为批量打印
        if (this.batchPrinting) {
          try {
            this.printRequests()
          }catch (e){
            this.requestFailed(e)
          }
        }else {
          let params = 'timeKey=1&bill_type=' + this.printTemplate.bill_type + '&condition=id:' + this.djno
          const URI = encodeURIComponent(params)
          this.reportId = this.printTemplate.id + '|' + URI
          // GrapeCity.ActiveReports.JSViewer.print({
          //   reportID: this.reportId,
          // })
          this.JsViewerPrint(this.reportId)
        }
      }else {
        this.$message.error(this.$t('arc.index.errorMsgSelectTemplate'))
      }
    },
    async printRequests() {
      for (let i of this.getCheckboxRecords()) {
        const printRes = await processPrintRequests(this.printTemplate.bill_type,i[this.disPlayFieldIdName],this.printTemplate.id)
        console.log(printRes)
        if (printRes.code != 0) {
          this.$message.error(this.$t(printRes.msg))
          break
        }
      }

    },
    async printDocument(requestData) {
      try{
        const printRes = await fetch("http://localhost:8899/print", {
          method: 'POST',
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(requestData)
        });
        const data = await printRes.json();
        if (data.code === 0) {
          console.log('打印成功');
          return true;  // 表示打印成功
        } else {
          console.log('打印失败');
          return false;  // 打印失败时返回 false
        }
      }catch (err) {
        return false;  // 打印失败时返回 false
      }
    },

    async processPrintRequests() {
      for (let i of this.getCheckboxRecords()) {
        let params = 'timeKey=1&bill_type=' + this.printTemplate.bill_type + '&condition=id:' + i[this.disPlayFieldIdName];
        const URI = encodeURIComponent(params);
        const printUrl = this.printTemplate.id + '|' + URI;
        //获取打印语言类型
        const printerLanguage = localStorage.getItem("printerLanguage");
        if (!printerLanguage || printerLanguage.length < 1) {
          this.$message.error(this.$t('arc.index.errorMsgSelectPrinterLanguage'));
          return;
        }
        try {
          const res = await fetch("/api/printins/ExportReport/" + printUrl + "/" + printerLanguage, { method: "GET" });
          if (!res.ok) {
            this.$message.error(this.$t('public.error'));
            return;
          }
          const priJson = await res.json();
          const printer = localStorage.getItem("printer");
          if (!printer || printer.length < 1) {
            this.$message.error(this.$t('arc.index.errorMsgSelectPrinter'));
            return;
          }
          let requestData = {
            numberArray: priJson,  // 传递获取到的打印数据
            printerName: printer,
            copies: 1
          };
          const printSuccess = await this.printDocument(requestData);  // 调用外部的打印函数
          if (!printSuccess) {
            this.$message.error(this.$t('public.error'));
            return;
          }
          await this.sleep(1500)
        } catch (err) {
          this.requestFailed(err);
          return;
        }
      }
    },

    async sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },
    // 打印插件入口
    JsViewerPrint(reportID){
      GrapeCity.ActiveReports.JSViewer.print({
        reportID,
      })
    },
    //预览弹窗下拉框事件
    upload(value) {
      if (!this.merge) {
        let params = 'timeKey=1&bill_type=' + this.bill_type + '&condition=id:' + value
        const URI = encodeURIComponent(params)
        this.reportId = this.printTemplate.id + '|' + URI
        this.$refs.arViewer.openReportEvent(this.reportId);
      }
    },
    //预览弹窗套版编辑按钮
    printEdit() {
      this.visible = false
      this.handleEdit()
    },
    //预览弹窗打印按钮
    async exPrint() {
      await this.$nextTick();
      const arViewer = this.$refs.arViewer;
      if (arViewer && typeof arViewer.expPrint === 'function') {
        this.$refs.arViewer.expPrint(this.printTemplate.id)
      } else {
        console.error('openReportEvent is not a function or arViewer is undefined.');
      }
    }
  }
}
</script>

<style lang='less' scoped>
.input-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0px;
}

.input-container label {
  margin-right: 5px;
}

.document-input {
  width: 200px;
}

:deep(.document-input .el-input__inner) {
  border: 1px solid rgb(178, 178, 178) !important;
  ;
}

.dialog-footer {
  padding: 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit_print {
  display: inline-block;
}

.footer-buttons {
  display: flex;
  gap: 10px;
}

.edit-print {
  margin-right: auto;
  /* Pushes the "套版编辑" button to the left */
}

.template_from {
  margin: 0 40px;
}

::v-deep .el-dialog__footer {
  padding: 0px;
}

::v-deep .el-dialog {
  border-radius: 10px;
}

::v-deep .el-dialog__title {
  border-radius: 10px;
  line-height: 30px;
  font-size: 14px;
  color: #303133;
}

::v-deep .el-dialog__header {
  padding: 2px 10px;
  height: 30px;
  border-radius: 10px 10px 0 0;
  background-color: #dce6e8 !important;
  color: #ffffff !important;
}

::v-deep .el-dialog__body {
  padding: 0px 10px;
}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 5px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}

::v-deep .el-table__header-wrapper {
  width: 100%;
  height: 30px;
}

::v-deep .el-table .el-table__cell {
  height: 30px;
  padding: 0px 0;
  min-width: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  text-overflow: ellipsis;
  vertical-align: middle;
  position: relative;
  text-align: left;
}

::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 8px;
}

::v-deep .el-date-editor--daterange.el-input__inner {
  width: 239px;
}
::v-deep .el-form-item__label {
  text-align: right;
}
.vxe-grid {

  ::v-deep .vxe-body--row {
    &.row--checked {
      background: var(--el-color-primary-light-8);
    }
  }

  ::v-deep .vxe-header--row {
    padding: 0;
    height: 35px;
    background-color: var(--el-color-primary-light-9);
    color: black !important;

    .vxe-header--column {
      font-weight: 500;
    }
  }

  // 个性化弹窗样式
  ::v-deep .vxe-modal--wrapper {

    .vxe-table-custom--checkbox-option {
      &.vxe-checkbox--icon.vxe-icon-checkbox-unchecked {
        &:focus {
          color: var(--el-color-primary) !important;
        }
      }

      &:hover {
        color: var(--el-color-primary) !important;
      }
    }

    .vxe-table-custom--checkbox-option.is--indeterminate {
      color: var(--el-color-primary) !important;

      .vxe-icon-checkbox-indeterminate-fill {
        color: var(--el-color-primary) !important;
      }
    }

    .is--checked {
      color: var(--el-color-primary) !important;

      .vxe-icon-checkbox-checked-fill {
        color: var(--el-color-primary) !important;
      }
    }

    .vxe-icon-checkbox-unchecked {
      color: var(--el-color-primary);

      &:hover {
        color: var(--el-color-primary);
      }
    }

    .vxe-radio-button>input:checked+.vxe-radio--label {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary);
    }

    .vxe-button.type--button.theme--primary:not(.is--disabled) {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary);
    }
  }

  ::v-deep .vxe-grid--pager-wrapper {
    .vxe-pager--num-btn.is--active {
      color: var(--el-color-primary);
    }
  }

}
</style>
