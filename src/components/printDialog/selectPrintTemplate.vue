<template>
  <div class="layout">
    <div style="display: flex;margin-top: 15px;">
      <div class="title">{{ $t('arc.overprint.printTemplate.title') }}</div>
      <el-form ref="ruleForm" label-position="left" label-width="auto" size="small" :model="ruleForm">
        <el-row :gutter="20">
          <el-col :span="24" :xs="24" style="height: 40px;">
            <el-form-item prop="connectString">
              <el-row>
                <el-col :span="4" :xs="24" class="find">
                  <el-select v-model="ruleForm.templateId" placeholder="">
                    <el-option v-for="item in nameOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4" :xs="24" class="find">
                  <el-select v-model="ruleForm.templateIdFind" placeholder="">
                    <el-option v-for="item in conditionOptions" :key="item.value" :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="8" :xs="24" class="find" style="margin-right: 30px">
                  <el-form-item>
                    <el-input v-model="ruleForm.id"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" :xs="24" class="find">
                  <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="findList()">{{ $t('public.query') }}</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24" :xs="24">
            <el-form-item prop="connectString">
              <el-row>
                <el-col :span="4" :xs="24" class="find">
                  <el-select v-model="ruleForm.templateName" placeholder="">
                    <el-option v-for="item in nameOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4" :xs="24" class="find">
                  <el-select v-model="ruleForm.templateNameFind" placeholder="">
                    <el-option v-for="item in conditionOptions" :key="item.value" :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="8" :xs="24" class="find">
                  <el-form-item>
                    <el-input v-model="ruleForm.name"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
    <el-divider></el-divider>
    <div class="">
      <vxe-grid ref="gridRef" border stripe resizable show-overflow show-header-overflow :height="tableHeight"
        header-align="center" :loading="loading" :columns="tableColumn" :data="tableData"
        :custom-config="{ mode: 'popup' }" :radio-config="{ trigger: 'row', highlight: true }" :pager-config="tablePage"
        @page-change="handlePageChange" @cell-dblclick="handleDblclick">
        <!-- 操作栏位的按钮模板 -->
        <template v-slot:actions="scope">
          <el-button @click="handleSelectClick(scope.row)"
            style="color: #14A380;border-color: rgb(185, 227, 217);background-color: rgb(232, 246, 242);" size="small">
            {{ $t('arc.overprint.printTemplate.select') }}
          </el-button>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>
<script>

import { reportconPage } from '@/api/interfaceList'
import Vue from 'vue';
export default {
  name: 'SelectPrintTemplate',
  props: {
    menuId: {
      type: [String, Number],
      default: ''
    },
    fromDataset: {
      type: Object,
      default: () => { }
    },
    isEdit: Boolean
  },
  data() {
    return {
      loading: true,
      ruleForm: {
        id: '',
        name: '',
        templateId: '',
        templateName: '',
        templateIdFind: '',
        templateNameFind: '',
      },
      nameOptions: [
        {
          value: 'template_id',
          label: this.$t('arc.overprint.table.templateNO')
        }, {
          value: 'template_name',
          label: this.$t('arc.overprint.table.templateName')
        }
      ],
      conditionOptions: [
        {
          value: 0,
          label: this.$t('arc.overprint.printTemplate.conditionLabel')
        }, {
          value: 1,
          label: this.$t('arc.overprint.printTemplate.conditionLabel1')
        }
      ],
      tablePage: {
        layouts: ['Total', 'Sizes', 'PrevPage', 'JumpNumber', 'NextPage', 'FullJump'],
        total: 0,
        currentPage: 1,
        pageSize: 20
      },
      tableData: [],
      tableHeight: '480px',
      tableColumn: [
        {
          field: 'is_default',
          align: "center",
          title: this.$t('arc.overprint.printTemplate.table.title'),
          width: 90,
          slots: {
            default: 'actions' // 指定自定义插槽
          }
        },
        { type: "seq", title: this.$t('arc.overprint.printTemplate.table.item'), width: '60', fixed: "center", align: "center" },
        { field: "code", title: this.$t('arc.overprint.table.templateNO'), },
        { field: "name", title: this.$t('arc.overprint.table.templateName'), },

      ],
    }
  },
  watch: {
    isEdit(newVal) {
      this.isEdit = newVal;
      this.edittwo = newVal;
    },
    fromDataset(newVal) {
      this.form = newVal
      this.getList()
    }
  },
  mounted() {
    Vue.set(this.ruleForm, 'templateId', 'template_id');
    Vue.set(this.ruleForm, 'templateName', 'template_name');
    Vue.set(this.ruleForm, 'templateIdFind', 1);
    Vue.set(this.ruleForm, 'templateNameFind', 1);
    this.findList()
    this.loading = false;
  },
  methods: {
    findList() {
      let query = {
        current: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
        bill_type: this.menuId,
        code: this.ruleForm.id,
        name: this.ruleForm.name,
        billTypeCondition: this.ruleForm.templateIdFind,
        nameCondition: this.ruleForm.templateNameFind
      }
      if (this.ruleForm.templateId == 'template_name' && this.ruleForm.id != '') {//用户切换查询字段
        query = {
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          bill_type: this.menuId,
          name: this.ruleForm.id,
          nameCondition: this.ruleForm.templateIdFind
        }
      } else if (this.ruleForm.templateName == 'template_id' && this.ruleForm.id != '') {
        query = {
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          bill_type: this.menuId,
          code: this.ruleForm.name,
          billTypeCondition: this.ruleForm.templateNameFind
        }
      }
      reportconPage(query).then(res => {
        this.tableData = res.data.records
      }).catch(err => {
        this.requestFailed(err)
      })
    },
    handleSelectClick(param) {
      this.$emit('selectId', param);
    },
    handlePageChange() {

    },
    handleDblclick({ row }) {
      this.handleSelectClick(row);
    }
  }
}
</script>
<style scoped lang="less">
.layout {
  //height: calc(100vh - 300px);
}

.title {
  font-weight: bold;
  width: 120px;
  padding-top: 8px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.find {
  margin-left: 10px;
}

::v-deep .el-dialog {
  border-radius: 10px;
}

::v-deep .el-dialog__title {
  border-radius: 10px;
  line-height: 30px;
  font-size: 14px;
  color: #303133;
}

::v-deep .el-dialog__header {
  padding: 2px 10px;
  height: 30px;
  border-radius: 10px 10px 0 0;
  background-color: #dce6e8 !important;
  color: #ffffff !important;
}

::v-deep .el-dialog__body {
  padding: 10px 10px;
}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 5px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}

.vxe-table.border--full .vxe-table--header-wrapper {
  background-color: rgb(234, 239, 238);
}

.el-divider--horizontal {
  display: block;
  height: 1px;
  width: 100%;
  margin: 10px 0;
}

.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}
::v-deep .vxe-grid--pager-wrapper {
  .vxe-pager--num-btn.is--active {
    color: var(--el-color-primary);
  }
}

.vxe-grid {

  ::v-deep .vxe-body--row {
    &.row--checked {
      background: var(--el-color-primary-light-8);
    }
  }

  ::v-deep .vxe-header--row {
    padding: 0;
    height: 35px;
    background-color: var(--el-color-primary-light-9);
    color: black !important;

    .vxe-header--column {
      font-weight: 500;
    }
  }

  // 个性化弹窗样式
  ::v-deep .vxe-modal--wrapper {

    .vxe-table-custom--checkbox-option {
      &.vxe-checkbox--icon.vxe-icon-checkbox-unchecked {
        &:focus {
          color: var(--el-color-primary) !important;
        }
      }

      &:hover {
        color: var(--el-color-primary) !important;
      }
    }

    .vxe-table-custom--checkbox-option.is--indeterminate {
      color: var(--el-color-primary) !important;

      .vxe-icon-checkbox-indeterminate-fill {
        color: var(--el-color-primary) !important;
      }
    }

    .is--checked {
      color: var(--el-color-primary) !important;

      .vxe-icon-checkbox-checked-fill {
        color: var(--el-color-primary) !important;
      }
    }

    .vxe-icon-checkbox-unchecked {
      color: var(--el-color-primary);

      &:hover {
        color: var(--el-color-primary);
      }
    }

    .vxe-radio-button > input:checked + .vxe-radio--label {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary);
    }

    .vxe-button.type--button.theme--primary:not(.is--disabled) {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary);
    }
  }

  ::v-deep .vxe-grid--pager-wrapper {
    .vxe-pager--num-btn.is--active {
      color: var(--el-color-primary);
    }
  }

  ::v-deep .vxe-cell {
    .el-input__inner {
      height: 33px;
      line-height: 33px;
    }

    .el-input__icon {
      line-height: 33px;
    }
  }
}
</style>
