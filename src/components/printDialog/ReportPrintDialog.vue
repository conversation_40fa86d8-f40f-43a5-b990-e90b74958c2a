<template>
  <div>
    <el-dialog
      :title='dialogTitle'
      style='padding: 0px;'
      width='30%'
      :visible.sync='dialogVisible'
      :close-on-click-modal='false'
      v-if='dialogVisible'
      top="40vh"
    >
      <div style='display:flex;'>
        <el-form label-width='85px' :rules='rules' ref='addEntityForm'
                 style='margin: 0px;padding: 0px;flex:1'>
          <el-row :gutter="15">
            <el-col :span="16" :xs="24">
              <el-form-item :label="$t('arc.componentsPrintDialog.index.printTemplate')" label-width="90px">
                <el-input v-model="code" size="mini" placeholder="">
                  <template #suffix>
                    <i class="el-icon-search" @click="handleIconClick"></i>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <div style="margin-top: 5px">
              <el-button type="primary" size="small" @click="handleEdit" plain>{{ $t('public.edit') }}</el-button>
              <el-button type="primary" size="small" @click="handleCrate" plain>{{ $t('public.add') }}</el-button>
            </div>

<!--            <el-col :span="4" :xs="24">-->
<!--              <el-form-item>-->
<!--                <el-button type="primary" size="small" @click="handleEdit" plain>编辑</el-button>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="4" :xs="24">-->
<!--              <el-button type="primary" size="small" @click="handleCrate" plain>新增</el-button>-->
<!--            </el-col>-->
          </el-row>
        </el-form>
      </div>
      <el-divider></el-divider>
      <div slot="footer" style="margin: 10px">
        <el-button @click="dialogVisible = false" size="small" style="margin-bottom: 10px;">{{ $t('arc.componentsPrintDialog.index.closeESC') }}</el-button>
        <el-button @click="previewEvent" size="small" style="margin-bottom: 10px;">{{ $t('arc.componentsPrintDialog.index.preview') }}</el-button>
        <el-button @click="loadViewer" type="primary" size="small" style="margin-bottom: 10px;">{{ $t('arc.componentsPrintDialog.index.print') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog height="400px" width="800px" :title="$t('arc.overprint.printTemplate.select')" :visible.sync='selectVisible' :close-on-click-modal='false'
               :destroy-on-close='true' append-to-body>
      <div class="sync-dialog__div">
        <PrintTemplate @selectId="handleSelectClick" :menuId="this.menuId"></PrintTemplate>
      </div>
    </el-dialog>
    <el-dialog
      :title="$t('arc.componentsPrintDialog.index.dialogTitlePrintPreview')"
      :visible.sync='visible'
      :close-on-click-modal='false'
      width='80%'
      top="7vh"
    >
      <ArViewer ref='arViewer' :report-id='reportId' :is-full-screen='false' />
      <div slot="footer" class="dialog-footer">
        <el-button @click="printEdit" size="small" class="edit_print">{{ $t('arc.componentsPrintDialog.index.templateEdit') }}</el-button>
        <div class="footer-buttons">
          <el-button @click="visible = false" size="small">{{ $t('arc.componentsPrintDialog.index.close') }}</el-button>
          <el-button type="primary" @click="exPrint" size="small">{{ $t('arc.componentsPrintDialog.index.title') }}</el-button>
        </div>
      </div>
    </el-dialog>
    <CreateTemplate ref="createTemplateRef"></CreateTemplate>
  </div>
</template>

<script>
import ArViewer from './ArViewer'
import PrintTemplate from '@/components/printDialog/selectPrintTemplate.vue'
import Vue from 'vue'
import CreateTemplate from '@/views/custommana/overprint/createTemplate.vue'
import { savePrintData } from '@/api/interfaceList'
import { processReportsPrintPdfRequests, sleep } from '@/utils/arPrintAgent'

export default {
  name: 'ReportPrintDialog',
  components: {
    CreateTemplate,
    PrintTemplate,
    ArViewer
  },
  data() {
    return {
      printType:'0', //打印类型 0单据打印 1报表打印 2报表数据打印标签
      menuId:'',
      selectVisible:false,
      visible: false,
      moreInfo: '',
      field: '',
      form: {},
      dialogTitle: '打印',
      dialogVisible: false,
      code:'',
      printTemplate:{},
      printData: {},
      rules: {
        id: [{ required: true, message: '必填:页面', trigger: 'blur' }]
      },
      datechoice: [],
      printmodal: [],
      reportId: '',
      modalshow: ''
    }
  },
  mounted() {
    // 添加键盘事件监听器
    window.addEventListener('keydown', this.handleKeydown);
  },
  // beforeDestroy() {
  //   // 移除键盘事件监听器
  //   window.removeEventListener('keydown', this.handleKeydown);
  // },
  methods: {
    handleKeydown(event) {
      if (event.key === 'F6') {
        this.previewEvent()
      } else if (event.key === 'F7') {
        this.loadViewer()
      }
    },
    create(printData) {
      let _that = this
      if (printData && printData.printType) {
        _that.printType = printData.printType
      }
      _that.menuId = printData.menuId
      _that.menuName = printData.menuName
      _that.selectRecords = printData.selectRecords
      _that.dialogVisible = true
    },
    //打印弹窗打印套版输入框按钮
    handleIconClick() {
      this.selectVisible = true
    },
    //选择弹窗选择按钮
    handleSelectClick(prams) {
      this.selectVisible = false
      this.printTemplate = prams
      Vue.set(this,'printTemplate', prams)
      this.code = prams.code
    },
    //打印弹窗编辑按钮
    handleEdit() {
      if (this.code != null && this.code.length > 0) {
        this.dialogVisible = false
        const username = this.$store.state.user.info.username
        //路由参数放缓存
        localStorage.setItem(username + "_bill_type", this.printTemplate.bill_type)
        localStorage.setItem(username + "_template_id", this.printTemplate.id)
        localStorage.setItem(username + "_refresh_print", true)//刷新页面标识
        this.$router.push({
          name: 'arcmodel',
          params: { id: this.printTemplate.id, name: this.printTemplate.name, reportId: this.printTemplate.id, bill_type: this.printTemplate.bill_type,subType:this.printTemplate.type },
          replace: true
        })
      }
    },
    //打印弹窗新增按钮
    handleCrate() {
      this.dialogVisible = false
      let entity = {}
      entity.is_default = false
      entity.language_code = 'zh-hans'
      entity.bill_type = this.menuId
      entity.bill_type_name = this.menuName
      this.$refs.createTemplateRef.setCreateVisible(true, entity)
    },
    //打印弹窗预览按钮
    async previewEvent() {
      if (this.code != null && this.code.length > 0) {
        this.visible = true
        await this.$nextTick(); // 等待 DOM 更新
        const resp = await this.savePData(this.selectRecords[0],this.menuId); // 预览之前先保存数据集
        if (resp.code === 0)
          this.preview() //当前this会丢失问题，所以换个方法
      }else {
        this.$message.error(this.$t('arc.index.errorMsgSelectTemplate'))
      }
    },
    preview() {
      let params = 'timeKey=1&bill_type=' + this.printTemplate.bill_type + '&printType=' + this.printType
      const URI = encodeURIComponent(params)
      this.reportId = this.printTemplate.id + '|' + URI
      const arViewer = this.$refs.arViewer;
      if (arViewer && typeof arViewer.openReportEvent === 'function') {
        arViewer.openReportEvent(this.reportId);
      } else {
        console.error('openReportEvent is not a function or arViewer is undefined.');
      }
    },
    //打印弹窗按钮
    async loadViewer() {
      if (this.code != null && this.code.length > 0) {
        if (this.selectRecords.length == 1) {
          const resp = await this.savePData(this.selectRecords[0],this.menuId)
          if (resp.code === 0) {
            let params = 'timeKey=1&bill_type=' + this.printTemplate.bill_type + '&printType=' + this.printType
            const URI = encodeURIComponent(params)
            this.reportId = this.printTemplate.id + '|' + URI
            GrapeCity.ActiveReports.JSViewer.print({
              reportID: this.reportId,
            })
          }
        }else { //批量打印
          for (let i = 0; i < this.selectRecords.length; i++) {
            const printDate = this.selectRecords[i];
            let copies = printDate['copies'];
            //判断属性是为空串
            if (copies !== undefined && copies.length < 1)
              copies = 1
            const resp = await this.savePData(printDate,this.menuId)
            if (resp.code === 0) {
              const printRes = await processReportsPrintPdfRequests(this.printTemplate.bill_type,this.printTemplate.id,this.printType,copies)
              // if (printRes.code != 0) {
              //   this.$message.error(this.$t(printRes.msg))
              //   break
              // }
            }
            await sleep(1500);
          }
          this.$message.success(this.$t('arc.index.successMsgPrint'))
        }
      }else {
        this.$message.error(this.$t('arc.index.errorMsgSelectTemplate'))
      }
    },
    //预览弹窗套版编辑按钮
    printEdit() {
      this.visible = false
      this.handleEdit()
    },
    //预览弹窗打印按钮
    async exPrint() {
      await this.$nextTick();
      const arViewer = this.$refs.arViewer;
      if (arViewer && typeof arViewer.expPrint === 'function') {
        this.$refs.arViewer.expPrint(this.printTemplate.id)
      } else {
        console.error('openReportEvent is not a function or arViewer is undefined.');
      }
    },
    //打印、预览前数据存储
    async savePData(data,menuId) {
      return await savePrintData({
        printData: data,
        billType: menuId
      })
    },
  }
}
</script>

<style lang='scss' scoped>

.dialog-footer {
  padding: 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.footer-buttons {
  display: flex;
  gap: 10px;
}
.edit-print {
  margin-right: auto;
  /* Pushes the "套版编辑" button to the left */
}
::v-deep .el-dialog__footer {
  padding: 0px;
}

::v-deep .el-dialog {
  border-radius: 10px;
}

::v-deep .el-dialog__title {
  border-radius: 10px;
  line-height: 30px;
  font-size: 14px;
  color: #303133;
}

::v-deep .el-dialog__header {
  padding: 2px 10px;
  height: 30px;
  border-radius: 10px 10px 0 0;
  background-color: #dce6e8 !important;
  color: #ffffff !important;
}

::v-deep .el-dialog__body {
  padding: 10px 10px 0px 10px;
}
::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 5px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}
::v-deep .el-form-item {
  margin-bottom: 8px;
}
</style>
