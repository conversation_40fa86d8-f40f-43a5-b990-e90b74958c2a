<style lang="scss" scoped>
#viewerContainer {
  background-color: #e5e5e5;
  width: 100%;
  height: calc(100vh - 220px);
}
body,html{
    background-color: white;
}
</style>

<template>
  <div id="viewerContainer" />
</template>

<script>
import '@grapecity/ar-viewer/dist/jsViewer.min.js'
import '@grapecity/ar-viewer/dist/jsViewer.min.css'
export default {
  name: '<PERSON>rViewer',
  props:{
    isFullScreen: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      viewer: null,
      // isFullScreen:true,
      reportId:null,
    }
  },
  watch: {
    // 监听reportId，调用openReport方法进行渲染界面
    reportId() {
      if (this.viewer) {
        this.viewer.openReport(this.reportId)
      }
    }
  },
  mounted() {
    // const designerSettings = JSON.parse(localStorage.getItem('designerSettings'))
    let ActiveReports = window.GrapeCity.ActiveReports
    this.viewer = new ActiveReports.JSViewer.create({ // eslint-disable-line
      element: '#viewerContainer',
      reportParameters: [{ name: '@id', values: ['Reports参数1']}],
      // locale: designerSettings ? designerSettings.locale : 'zh',
      reportService: {
        url: '/arc/api/reporting'
      },
      settings: {
        zoomType: 'FitPage'
      },
      // 导出设置
      // defaultExportSettings: {
      //   pdf: {
      //     // /* only for rpx*/
      //     // ConvertMetaToPng: { value: true },
      //     // ExportBookmarks: { value: false },
      //     // ImageInterpolation: { value: 'Auto' },
      //     // ImageQuality: { value: 'Highest' },
      //     // /* both (rdlx and rpx)*/
      //     // Title: { value: 'Document' },
      //     // Author: { value: 'USER' },
      //     // Subject: { value: 'PDF' },
      //     // Keywords: { value: 'PDF export' },
      //     // Application: { value: 'AR16' },
      //     // EmbedFonts: { value: 'All' },
      //     // Version: { value: 'PDF-1.5' },
      //     // UserPassword: { value: 'user_pwd' },
      //     // OwnerPassword: { value: 'owner_pwd' },
      //     // Encrypt: { value: true },
      //     // FileName: { value: 'ar16_pdf', visible: true }
      //   }
      // },
      documentLoaded: () => {
        if (this.isFullScreen) {
          const fullButton = document.getElementById('main_toolbar_Item_14').children[0]
          fullButton.click() // 从打印按钮进来则全屏幕展示
        }
      },
      reportLoaded() {
      }
    })
  },
  methods: {
    // 设计器编辑后再预览，需要重新调用openReport
      openReportEvent (id) {
        this.reportId = id
          if (this.viewer) {
              this.viewer.openReport(this.reportId)
            if (this.isFullScreen) {
              const fullButton = document.getElementById('main_toolbar_Item_14').children[0]
              fullButton.click() // 从打印按钮进来则全屏幕展示
            }
      }
    },
    openReportEventObj (id,obj) {
      this.reportId = id
      if (this.viewer) {
        this.viewer.openReport(this.reportId,obj)
        if (this.isFullScreen) {
          const fullButton = document.getElementById('main_toolbar_Item_14').children[0]
          fullButton.click() // 从打印按钮进来则全屏幕展示
        }

      }
    },
    expPrint(id) {
      if (this.viewer) {
        this.viewer.print();   
      }
    }
  }
}
</script>
