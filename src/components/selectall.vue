<template>
  <div>
    <a-modal title="作业人员选择" destroyOnClose width="40%" :visible="visible" :confirmLoading="confirmLoading"
      @cancel="handleCancel">
      <a-form layout="inline" :form="form">
        <a-form-item label="代号">
          <a-input v-model="queryParam.salNo" size="default" :placeholder="$t('请输入代号')" />
        </a-form-item>
        <a-form-item label="名称">
          <a-input v-model="queryParam.name" size="default" :placeholder="$t('请输入名称')" />
        </a-form-item>
        <a-row>
          <a-col :md="3" :sm="24">
            <a-button type="primary" @click="getList">{{ $t('public.query') }}</a-button>
          </a-col>
          <a-col :md="3" :sm="24">
            <a-button style="margin-left: 8px" @click="reset">{{ $t('public.reset') }}</a-button>
          </a-col>
        </a-row>
      </a-form>
      <el-table ref="xTable" :data="tableData" row-key="id" max-height="450px"
        :header-cell-style="{ backgroundColor: '#F4F5F9', color: '#7A8085', fontWeight: '400', fontSize: '12px', verticalAlign: 'top' }"
        :cell-style="{ fontSize: '12px', verticalAlign: 'top' }" :default-expand-all="true" size="small"
        @selection-change="handleSelectionsup">
        <el-table-column type="selection" width="80" align="center" :reserve-selection="true" />
        <el-table-column prop="salNo" align="center" label="代号"></el-table-column>
        <el-table-column prop="name" align="center" label="名称"></el-table-column>
      </el-table>
      <div style="margin-top: 10px;">
        <el-pagination background :current-page="tablePage.currentPage" :pager-count="5" :page-sizes="[10, 20, 50, 100]"
          :page-size="20" layout="total, sizes,prev, pager, next" :total="tablePage.total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
      <!-- layout="total, sizes, prev, pager, next, jumper" -->

      <!-- <el-pagination background :pager-count="5" :page-sizes="[5,10, 30, 50]" :page-size="10"
        @size-change="goods_pageSizeChange" :current-page="goods_currentPage" @current-change="goods_currentChange"
        layout="prev, pager, next" :total="goods_pickerCount" style="margin-top: 5px;"></el-pagination> -->
      <!-- <vxe-table size="small" border show-overflow highlight-hover-row ref="xTable" class="radio-table"
        @checkbox-change="selectChangeEvent" :checkbox-config="{trigger: 'row', range: true}"
        :radio-config="{labelField: '', trigger: 'row'}" :data="tableData">
        <vxe-table-column type="checkbox" width="50"></vxe-table-column>
        <vxe-table-column field="bilNo" title="单号" align="center"></vxe-table-column>
      </vxe-table>
      <vxe-pager :current-page="tablePage.currentPage" :page-size="tablePage.pageSize" :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']" @page-change="handlePageChange">
      </vxe-pager> -->
      <template slot="footer">
        <a-button key="ok" @click="save">{{ $t('public.sure') }}</a-button>
        <a-button key="cancel" @click="handleCancel">{{ $t('public.cancel') }}</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script>
  import { salmquery } from '@/api/mes/quality'
  import {
    fetchList,
  } from '@/api/admin/role'
  import {
    mapState
  } from 'vuex'
  export default {
    name: 'SetList',
    props: {
    },
    data() {
      return {
        title: '',
        tableData: [],
        row: {},
        queryParam: {
          salNo: '',
          name: ''
        },
        selectRecords: [],
        visible: false,
        visible1: false,
        confirmLoading: false,
        treeData: [],
        currentPage: 1,
        form: this.$form.createForm(this),
        tablePage: {
          currentPage: 1,
          pageSize: 20,
          total: 0
        },
        text: [],
        idlist: '',
        typeGroupNo: '',
        keyword: '',
        addtime: false,
        multipleSelection: []
      }
    },
    watch: {
    },
    computed: {
      ...mapState({
        propertyList: state => state.approval.propertyList
      }),
    },
    created() {

    },
    methods: {
      handleCurrentChange(currentPage) {
        this.tablePage.currentPage = currentPage
        this.getList()
      },
      handleSizeChange(pageSize) {
        this.tablePage.pageSize = pageSize
        this.getList()
      },
      // 分页
      // handlePageChange({ currentPage, pageSize }) {
      //   this.tablePage.currentPage = currentPage
      //   this.tablePage.pageSize = pageSize
      //   this.getList()
      // },
      handleSelectionsup(val) {
        this.multipleSelection = val
      },
      // 分页触发事件
      click() {
        let arr = this.propertyList.filter(i => {
          return i.page === this.tablePage.currentPage
        })
        let showList = []
        arr.forEach(e => {
          showList.push(this.tableData[e.rowIndex])
        });
        this.$refs.xTable.setCheckboxRow([...showList], true)
        const selectRecords = this.$refs.xTable.getCheckboxRecords()
        this.text.push(...selectRecords)
      },
      open(typeGroupNo) {
        console.log(typeGroupNo, 'bbbbbbbbbb')
        if (typeGroupNo == 'SC' || typeGroupNo == 'SC_QTY') {
          this.addtime = true
        } else {
          this.addtime = false
        }

        this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
        this.queryParam.staDd = moment().startOf('months').format('YYYY-MM-DD')
        this.typeGroupNo = typeGroupNo
        // this.idlist = id
        // this.row = model
        this.keyword = ''
        this.getList()
        this.visible = true
      },
      getList(data) {
        this.loading = true
        if (this.addtime) { } else {
          this.queryParam = {}
        }
        salmquery({
          // bilType: this.typeGroupNo,
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          ...this.queryParam
        }).then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        }).catch(err => {
          this.requestFailed(err)
          this.loading = false
        })
          .finally(() => {
            this.loading = false
          })
      },

      // 重置搜索内容
      reset() {
        this.queryParam = {}
      },
      selectChangeEvent({ checked, records, rowIndex }) {
        // let obj = {
        //   page: this.tablePage.currentPage,
        //   rowIndex: rowIndex
        // }
        // if (checked) {
        //   this.$store.commit("SET_PROPER", obj);
        // } else {
        //   this.$store.commit("DEL_PROPER", obj);
        // }
      },
      save() {
        // let list = this.$refs.xTable.getCheckboxRecords()
        const zyList = this.multipleSelection.map(obj => obj.salNo).join(',');
        console.log(zyList, 'wwbbbbbbbb')
        this.$emit('zyList', zyList)
        this.$nextTick(() => {
          this.multipleSelection = []
          this.tableData = []
          this.handleCancel()
        })
      },

      handleCancel() {
        this.visible = false
      }
    }
  }
</script>