<template>

      <div
        v-if="visible"
        :class="[fixedHeader && 'ant-header-fixedHeader', sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed', ]"
        :style="{ padding: '0' }"
      >
        <div
          v-if="mode === 'sidemenu'"
          class="header"
        >
          <div class="left-section">
            <img
              src="../../assets/newLogin/homeLogo.png"
              class="logo-header"
            >
            <span class="title-header">JUST MAKE</span>
            <i
              v-if="device==='mobile'"
              class="trigger"
              @click="toggle"
              :class="device==='mobile' ? 'el-icon-menu':'el-icon-s-grid'"></i>
            <i
              v-else
              class="trigger"
              @click="toggle"
              :class="device==='mobile' ? 'el-icon-s-grid':'el-icon-menu'"></i>
          </div>

          <user-menu></user-menu>
        </div>

        <div
          v-else
          :class="['top-nav-header-index', theme]"
        >
          <div class="header-index-wide">
            <div class="header-index-left">
              <logo
                class="top-nav-header"
                :show-title="device !== 'mobile'"
              />
              <s-menu
                v-if="device !== 'mobile'"
                mode="horizontal"
                :menu="menus"
                :theme="theme"
                :i18n-render="i18nRender"
              />
              <a-icon
                v-else
                class="trigger"
                :type="collapsed ? 'menu-fold' : 'menu-unfold'"
                @click="toggle"
              />
            </div>

            <user-menu></user-menu>
          </div>

        </div>
      </div>
</template>

<script>
import UserMenu from '../tools/UserMenu'
import SMenu from '../Menu/'
import Logo from '../tools/Logo'

import { i18nRender } from '@/locales'
import { mixin, mixinDevice } from '@/utils/mixin'
export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    Logo
  },
  mixins: [mixin, mixinDevice],
  props: {
    mode: {
      type: String,
      required: false,
      default: 'inline'
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    collapsible: {
      type: Boolean,
      required: false,
      default: false
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    menus: {
      type: Array,
      required: true
    }

  },
  filters: {
    applyStatus (val) {
      return i18nRender(val)
    }
  },
  data () {
    return {
      name: '',
      breadList: [],
      visible: true,
      oldScrollTop: 0
    }
  },
  watch: {
    $route () {
      this.getBreadcrumb()
    }
  },
  mounted () {
    this.getBreadcrumb()
    document.addEventListener('scroll', this.handleScroll, { passive: true })
  },
  methods: {
    i18nRender,
    getBreadcrumb () {
      this.breadList = []
      this.name = this.$route.name
      this.$route.matched.forEach(item => {
        // item.name !== 'index' && this.breadList.push(item)
        this.breadList.push(item)
      })
    },
    handleScroll () {
      if (!this.autoHideHeader) {
        return
      }
      const scrollTop = document.body.scrollTop + document.documentElement.scrollTop
      if (!this.ticking) {
        this.ticking = true
        requestAnimationFrame(() => {
          if (this.oldScrollTop > scrollTop) {
            this.visible = true
          } else if (scrollTop > 300 && this.visible) {
            this.visible = false
          } else if (scrollTop < 300 && !this.visible) {
            this.visible = true
          }
          this.oldScrollTop = scrollTop
          this.ticking = false
        })
      }
    },
    toggle () {
      this.$emit('toggle')
      this.$bus.$emit('rIsActive')
    }
  },
  beforeDestroy () {
    document.body.removeEventListener('scroll', this.handleScroll, true)
  }
}
</script>

<style lang="less">
@import '../index.less';
.ant-header-fixedHeader.ant-header-side-opened {
  width: 100% !important;
}
.header {
  display: flex;
  flex-wrap: nowrap; /* 保持子元素在同一行内 */
  justify-content: space-between; /* 子元素之间的间距 */
  align-items: center; /* 子元素在交叉轴（垂直方向）上的对齐方式 */
  background-color: var(--el-color-primary-light-1);
  width: 100%;
  height: 48px;
}
.left-section {
  display: flex;
  align-items: center;
  width: auto;
}
.logo-header{
  height: 28px; /* 固定高度 */
  margin:11px 10px 11px 15px;
}

.title-header{
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #f8f8f5;
  line-height: 25px;
  text-align: center;
  font-style: normal;
  letter-spacing: 1px; /* 设置字体间距 */
  margin:11px 23px 12px 0px;
}
.trigger {
  font-size: 24px;
  //margin-left: 55px;
  left: 23px;
  color:white;
}

.header-animat {
  position: relative;
  height: 48px;
  width: 100%;
}

.ant-tabs-nav-container {
  margin-bottom: 1px !important;
  font-size: 7px !important;
  line-height: 4 !important;
}
</style>
