<template>
  <a-badge :count="count">
    <a-popover v-model="visible" trigger="click" placement="bottomRight" overlayClassName="header-notice-wrapper"
      :getPopupContainer="() => $refs.noticeRef.parentElement" :autoAdjustOverflow="true" :arrowPointAtCenter="true"
      :overlayStyle="{ width: '300px', top: '50px' }">

      <template slot="content">
        <a-spin :spinning="loading">
          <a-tabs default-active-key="2" @tabClick="tabClick">
            <a-tab-pane tab="通知" key="1">
              <a-list>
                <a-list-item v-for="i in this.tableData" :key="i.id">
                  <div @click="clear">
                    <router-link :to="{name: 'messageCenter'}">
                      <a-list-item-meta :title="i.title" :description="i.data">
                      </a-list-item-meta>
                    </router-link>
                  </div>

                </a-list-item>
              </a-list>
            </a-tab-pane>
            <a-tab-pane tab="信息" key="2">
              <a-list>
                <a-list-item v-for="i in this.tableData" :key="i.id">
                  <div @click="clear">
                    <router-link :to="{name: 'messageCenter'}">
                      <a-list-item-meta :title="i.title" :description="i.data">
                      </a-list-item-meta>
                    </router-link>
                  </div>

                </a-list-item>
              </a-list>
            </a-tab-pane>
            <a-tab-pane tab="待办" key="3">
              <a-list>
                <a-list-item v-for="i in this.tableData" :key="i.id">
                  <div @click="clear">
                    <router-link :to="{name: 'messageCenter'}">
                      <a-list-item-meta :title="i.title" :description="i.data">
                      </a-list-item-meta>
                    </router-link>
                  </div>

                </a-list-item>
              </a-list>
            </a-tab-pane>
          </a-tabs>
        </a-spin>
      </template>
      <span @click="fetchNotice" class="header-notice" ref="noticeRef" style="padding: 0 18px">
        <a-badge count="0">
          <a-icon style="font-size: 16px; padding: 4px" type="bell" />
        </a-badge>
      </span>
    </a-popover>
  </a-badge>

</template>

<script>
  // import axios from '@/router/axios'

  import { getMsg } from '@/api/aps/message'

  export default {
    name: 'HeaderNotice',
    data() {
      return {
        loading: false,
        visible: false,
        tableData: [],
        count: '',
        timer: null
      }
    },
    created() {
      // 定时器开启五分钟刷新一次300000
      this.getList()
      if (this.count > 0) {
        this.$notification.success({
          message: this.$t('public.sendMsg'),
          description: this.$t('public.sendMsgs'),
        })
      }
      // this.timer = setInterval(() => {
      //   this.getList()
      //   if (this.count > 0) {
      //     this.$notification.success({
      //       message: this.$t('public.sendMsg'),
      //       description: this.$t('public.sendMsgs'),
      //     })
      //   }
      // }, 300000);
    },
    mounted() {
      this.$bus.$on("onslected", () => {
        this.getList(2)
      })
    },
    destroyed() {
      clearInterval(this.timer)
      this.$bus.$off("onslected")
    },
    methods: {
      tabClick(obj) {
        let val
        if (obj === '1') {
          val = 2
        } else if (obj === '2') {
          val = 1
        } else if (obj === '3') {
          val = 3
        }
        this.getList(val)
      },
      fetchNotice() {
        if (!this.visible) {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            // this.count = ''
            this.getList(1)
          }, 500)
        } else {
          this.loading = false
        }
        this.visible = !this.visible
      },
      clear() {
        // this.count = ''
      },
      getList(msgType) {
        getMsg(
          Object.assign({
            current: 1,
            size: 3,
            msgType: msgType || 1,
            type: 1,
            state: 0
          })
        ).then(res => {
          this.tableData = []
          this.tableData = res.data.records
          this.count = res.data.total
          this.loading = false
        })
          .catch(err => {
            this.count = ''
            this.tableData = []
            this.loading = false
            this.requestFailed(err)
          })
      },
    }
  }
</script>

<style lang="css">
  .header-notice-wrapper {
    top: 50px !important;
  }

  .ant-badge-count {
    position: absolute;
    top: 5px !important;
    right: 20px !important;
    -webkit-transform: translate(50%, -50%);
    transform: translate(50%, -50%);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
</style>
<style lang="less" scoped>
  .header-notice {
    display: inline-block;
    transition: all 0.3s;

    span {
      vertical-align: initial;
    }
  }
</style>