import Vue from 'vue'
import router from './router'
import store from './store'

import NProgress from 'nprogress' // progress bar
import '@/components/NProgress/nprogress.less' // progress bar custom style
import notification from 'ant-design-vue/es/notification'
import { setDocumentTitle, domTitle } from '@/utils/domUtil'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { i18nRender } from '@/locales'
import { loginIvx } from '@/api/ivx'
import Cookies from 'js-cookie'
import { initBackEndControlRoutes } from '@/router/backEnd'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['login', 'iframePage'] // no redirect whitelist
const defaultRoutePath = '/dashboard/workplace'

router.beforeEach(async (to, from, next) => {
  NProgress.start() // start progress bar
  const title = i18nRender(to.meta.title)
  to.meta && typeof to.meta.title !== 'undefined' && setDocumentTitle(`${title} - ${domTitle}`)
  if (Vue.ls.get(ACCESS_TOKEN)) {
    /* has token */
    if (to.path === '/login/login') {
      next({ path: defaultRoutePath })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        try{
          // 初始化用户信息
          await store.dispatch('GetInfo')
          // 后端控制路由：路由数据初始化，防止刷新时丢失
          await initBackEndControlRoutes();
          const redirect = decodeURIComponent(to.path)
          if (to.path === redirect) {
            next({ ...to, replace: true })
          } else {
            next({ path: redirect })
          }
        }catch (err){
          console.error(err)
          notification.error({
            message: '错误',
            description: '登录失效，请重新登陆!'
          })
          store.dispatch('Logout').then(() => {
            next({ path: '/login/login', query: { redirect: to.fullPath } })
          })
        }
      } else {
        await registerKey(to)
        await logoutKey(from)
        next()
      }
    }
  } else {
    if (whiteList.includes(to.name)) {
      // 在免登录白名单，直接进入
      next()
    } else {
      if (to.name !== 'login') {
        next({ path: '/login/login', query: { redirect: to.fullPath } })
        NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      }
    }
  }
})
router.afterEach((to, from) => {
  NProgress.done() // finish progress bar
  handleKeepAlive(to)
})

// 移除多层嵌套路由，解决多级路由缓存失效问题
function handleKeepAlive(to) {
  if (to.matched && to.matched.length > 2) {
    for (let i = 0; i < to.matched.length; i++) {
      const element = to.matched[i]
      if (element.components.default?.name === 'BasicLayout') {
        to.matched.splice(i, 1)
        handleKeepAlive(to)
      }
    }
  }
}
// 注册页面
function registerKey(route) {
  if (route.meta.permission) {
    const currentRoute = route.meta.permission[0];
    store.dispatch('Logkey', { key: currentRoute }).then((res) => {
    }).catch((err) => { })
  }
}
// 注销页面
function logoutKey(route) {
  if (route.meta.permission) {
    const currentRoute = route.meta.permission[0];
    store.dispatch('Remkey', { key: currentRoute }).then((res) => {
    }).catch((err) => { })
  }
}