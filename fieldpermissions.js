export const fieldJson = {
  srm_pt_fb: [
    {
      label: "多选",
      prop: 'checkBox',
      isCheck: true,
    },
    {
      label: "单据别",
      prop: 'type',
      isCheck: true,
      // width: 70
    },
    {
      label: "受订计划",
      prop: 'osNo',
      isCheck: true,
      // width: 160
    },
    {
      label: "单号",
      prop: 'bilNo',
      isCheck: true,
      // width: 150
    },
    {
      label: "品号",
      prop: 'prdNo',
      isCheck: true,
      width: 150
    },
    {
      label: "品名",
      prop: 'prdName',
      isCheck: true,
      width: 150
    },
    {
      label: "检验否",
      prop: 'chkMsg',
      isCheck: true,
      width: 150
    },
    {
      label: "单位",
      prop: 'unitName',
      isCheck: true,
      width: 50
    },
    {
      label: "单据类别",
      prop: 'bilTypeName',
      isCheck: true,
      width: 100
    },
    {
      label: "报交地",
      prop: 'whName',
      isCheck: true,
      // width: 150
    },
    {
      label: "数量",
      prop: 'qty',
      isCheck: true,
      // width: 100
    },
    {
      label: "报交数量",
      prop: 'qtyBj',
      isCheck: true,
      // width: 100
    },
    {
      label: "单价",
      prop: 'up',
      isCheck: true,
      // width: 100
    },
    {
      label: "扣税类别",
      prop: 'taxId',
      isCheck: true,
      // width: 100
    },
    {
      label: "金额",
      prop: 'amtn',
      isCheck: true,
      // width: 100
    },
    {
      label: "付款方式",
      prop: 'payTypeName',
      isCheck: true,
      width: 100
    },
    {
      label: "含税金额",
      prop: 'amtInTax',
      isCheck: true,
      width: 100
    },
    {
      label: "含税单价",
      prop: 'upInTax',
      isCheck: true,
      width: 100
    },
    {
      label: "税率",
      prop: 'taxRto',
      isCheck: true,
      width: 100
    },
    {
      label: "币别",
      prop: 'curName',
      isCheck: true,
      width: 100
    },
    // {
    //   label: "外币金额",
    //   prop: 'curAmount',
    //   isCheck: true,
    //   width: 100
    // },
    {
      label: "预交日期",
      prop: 'estDd',
      isCheck: true,
      width: 150
    },
    {
      label: "发布日期",
      prop: 'sysDate',
      isCheck: true,
      width: 150
    },
    {
      label: "供应商代号",
      prop: 'cusNo',
      isCheck: true,
      width: 100
    },
    {
      label: "供应商",
      prop: 'cusName',
      isCheck: true,
      // width: 150
    },
    {
      label: "合同号",
      prop: 'contractNo',
      isCheck: true,
      width: 150
    }
  ]
}