{"name": "vue-antd-pro", "version": "2.1.0", "private": true, "scripts": {"pre": "cnpm install || yarn --registry https://registry.npm.taobao.org || npm install --registry https://registry.npm.taobao.org ", "dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "analyz": "NODE_ENV=production npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "build:preview": "vue-cli-service build --mode preview", "lint:nofix": "vue-cli-service lint --no-fix", "postinstall": "opencollective-postinstall"}, "dependencies": {"@ant-design-vue/pro-layout": "^0.3.12", "@antv/data-set": "^0.10.2", "@femessage/el-semver-input": "^1.1.4", "@fullcalendar/core": "^4.4.1", "@fullcalendar/daygrid": "^4.4.2", "@fullcalendar/interaction": "^4.4.2", "@fullcalendar/list": "^4.4.0", "@fullcalendar/resource-timeline": "^4.4.0", "@fullcalendar/timegrid": "^4.4.2", "@fullcalendar/vue": "^4.4.2", "@grapecity/ar-designer": "17.2.0", "@grapecity/ar-viewer": "17.2.0", "@jiaminghi/data-view": "^2.10.0", "ant-design-vue": "^1.6.2", "axios": "^1.6.8", "core-js": "^3.1.2", "crypto-js": "^3.1.9-1", "echarts": "^4.8.0", "element-ui": "^2.13.2", "enquire.js": "^2.1.6", "file-saver": "^2.0.5", "getmac": "^5.16.0", "iview": "^3.5.4", "js-cookie": "^2.2.0", "js-table2excel": "^1.0.3", "jsbarcode": "^3.11.0", "jsencrypt": "^3.3.2", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "mammoth": "^1.6.0", "md5": "^2.2.1", "mockjs2": "1.0.8", "moment": "^2.24.0", "node-sass": "^4.14.1", "nprogress": "^0.2.0", "qrcodejs2": "0.0.2", "sockjs-client": "^1.0.0", "sortablejs": "^1.12.0", "splitpanes": "^2.4.1", "stompjs": "2.3.3", "store": "^2.0.12", "tiff.js": "^1.0.0", "v-charts": "^1.19.0", "v-viewer": "^1.5.1", "video.js": "^8.22.0", "viser-vue": "^2.4.6", "vue": "2.7.16", "vue-axios": "^2.1.5", "vue-clipboard2": "^0.2.1", "vue-cropper": "0.4.9", "vue-echarts": "^5.0.0-beta.0", "vue-grid-layout": "^2.4.0", "vue-draggable-plus": "^0.2.5", "vue-i18n": "^8.18.2", "vue-json-editor": "^1.4.0", "vue-ls": "^3.2.1", "vue-pdf": "^4.3.0", "vue-print-nb": "^1.5.0", "vue-quill-editor": "^3.0.6", "vue-resource": "^1.5.1", "vue-router": "^3.3.4", "vue-svg-component-runtime": "^1.0.1", "vue-video-player": "^5.0.0", "vuex": "^3.4.0", "vxe-table": "3.8.24", "vxe-table-plugin-antd": "^1.7.0", "vxe-table-plugin-element": "^1.7.0", "vxe-table-plugin-export-xlsx": "^1.2.2", "vxe-table-plugin-iview": "^1.7.0", "vxe-table-plugin-menus": "^1.2.0", "vxe-utils": "^1.8.0", "wangeditor": "^3.1.1", "webpack-bundle-analyzer": "^4.3.0", "webstomp-client": "^1.2.6", "xe-utils": "2.6.5", "xlsx": "^0.16.2"}, "devDependencies": {"@ant-design/colors": "^3.2.1", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-eslint": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.29", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.12.2", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^1.1.12", "eslint": "^5.16.0", "eslint-plugin-html": "^5.0.0", "eslint-plugin-vue": "^5.2.3", "git-revision-webpack-plugin": "^3.0.6", "less": "^3.9.0", "less-loader": "^4.1.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "sass-loader": "^8.0.2", "svg-sprite-loader": "^6.0.11", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "2.7.16", "webpack-theme-color-replacer": "^1.3.12"}}